import 'package:flutter/material.dart';
import '../screens/favorites_list_screen.dart';

class SortFilterBottomSheet extends StatefulWidget {
  final SortOption sortOption;
  final bool sortAscending;
  final FilterOption filterOption;
  final Function(SortOption, bool) onSortChanged;
  final Function(FilterOption) onFilterChanged;

  const SortFilterBottomSheet({
    super.key,
    required this.sortOption,
    required this.sortAscending,
    required this.filterOption,
    required this.onSortChanged,
    required this.onFilterChanged,
  });

  @override
  State<SortFilterBottomSheet> createState() => _SortFilterBottomSheetState();
}

class _SortFilterBottomSheetState extends State<SortFilterBottomSheet> {
  late SortOption _sortOption;
  late bool _sortAscending;
  late FilterOption _filterOption;

  @override
  void initState() {
    super.initState();
    _sortOption = widget.sortOption;
    _sortAscending = widget.sortAscending;
    _filterOption = widget.filterOption;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '排序和筛选',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 排序选项
          const Text(
            '排序方式',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          
          ...SortOption.values.map((option) {
            return RadioListTile<SortOption>(
              title: Text(_getSortOptionName(option)),
              value: option,
              groupValue: _sortOption,
              onChanged: (value) {
                setState(() {
                  _sortOption = value!;
                });
              },
              contentPadding: EdgeInsets.zero,
            );
          }),
          
          const SizedBox(height: 8),
          
          // 排序方向
          SwitchListTile(
            title: const Text('升序排列'),
            subtitle: Text(_sortAscending ? '从小到大' : '从大到小'),
            value: _sortAscending,
            onChanged: (value) {
              setState(() {
                _sortAscending = value;
              });
            },
            contentPadding: EdgeInsets.zero,
          ),
          
          const SizedBox(height: 16),
          
          // 筛选选项
          const Text(
            '筛选条件',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          
          ...FilterOption.values.map((option) {
            return RadioListTile<FilterOption>(
              title: Text(_getFilterOptionName(option)),
              value: option,
              groupValue: _filterOption,
              onChanged: (value) {
                setState(() {
                  _filterOption = value!;
                });
              },
              contentPadding: EdgeInsets.zero,
            );
          }),
          
          const SizedBox(height: 24),
          
          // 操作按钮
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _resetToDefault,
                  child: const Text('重置'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: FilledButton(
                  onPressed: _applyChanges,
                  child: const Text('应用'),
                ),
              ),
            ],
          ),
          
          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  String _getSortOptionName(SortOption option) {
    switch (option) {
      case SortOption.dateCreated:
        return '创建时间';
      case SortOption.dateUpdated:
        return '更新时间';
      case SortOption.title:
        return '标题';
      case SortOption.category:
        return '分类';
      case SortOption.rating:
        return '评分';
    }
  }

  String _getFilterOptionName(FilterOption option) {
    switch (option) {
      case FilterOption.all:
        return '全部';
      case FilterOption.public:
        return '公开的';
      case FilterOption.private:
        return '私密的';
      case FilterOption.withImages:
        return '有图片的';
      case FilterOption.withRating:
        return '有评分的';
    }
  }

  void _resetToDefault() {
    setState(() {
      _sortOption = SortOption.dateCreated;
      _sortAscending = false;
      _filterOption = FilterOption.all;
    });
  }

  void _applyChanges() {
    widget.onSortChanged(_sortOption, _sortAscending);
    widget.onFilterChanged(_filterOption);
    Navigator.of(context).pop();
  }
}
