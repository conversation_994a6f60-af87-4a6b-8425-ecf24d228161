class AppConstants {
  // 应用信息
  static const String appName = 'MyMapFavs';
  static const String appNameChinese = '我的喜好地图';
  static const String appVersion = '1.0.0';
  
  // 数据库相关
  static const String databaseName = 'MyMapFavs.db';
  static const int databaseVersion = 1;
  
  // 地图相关
  static const String amapApiKey = '7e2b9a68a29a502de59bca3affb3c68d';
  static const double defaultZoomLevel = 16.0;
  static const double defaultLatitude = 39.9042; // 北京
  static const double defaultLongitude = 116.4074;
  
  // 分类常量
  static const List<String> defaultCategories = [
    '餐厅',
    '咖啡馆',
    '书店',
    '景点',
    '购物',
    '工作',
    '旅行',
    '其他',
  ];
  
  // 分类颜色
  static const Map<String, String> categoryColors = {
    '餐厅': '#FF6B6B',
    '咖啡馆': '#4ECDC4',
    '书店': '#45B7D1',
    '景点': '#96CEB4',
    '购物': '#FFEAA7',
    '工作': '#DDA0DD',
    '旅行': '#98D8C8',
    '其他': '#F7DC6F',
  };
  
  // 文件路径
  static const String imagesPath = 'images';
  static const String markersPath = 'markers';
  
  // 分享相关
  static const String shareBaseUrl = 'https://mymapfavs.com/share/';
  
  // 设置键名
  static const String keyFirstLaunch = 'first_launch';
  static const String keyThemeMode = 'theme_mode';
  static const String keyDefaultPrivacy = 'default_privacy';
  static const String keyMapType = 'map_type';
  static const String keyAutoBackup = 'auto_backup';
  static const String keyLocationPermission = 'location_permission';
  
  // 限制
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int maxDescriptionLength = 500;
  static const int maxTitleLength = 100;
  static const int maxTagsCount = 10;
}
