class AppConstants {
  // 应用信息
  static const String appName = 'FavsAny';
  static const String appNameChinese = '万物收藏';
  static const String appVersion = '1.0.0';
  static const String appSlogan = '收藏你的一切喜好';
  static const String appDescription = '一个全能的个人兴趣收藏管理工具';

  // 数据库相关
  static const String databaseName = 'FavsAny.db';
  static const int databaseVersion = 1;
  
  // 地图相关
  static const String amapApiKey = '7e2b9a68a29a502de59bca3affb3c68d';
  static const double defaultZoomLevel = 16.0;
  static const double defaultLatitude = 39.9042; // 北京
  static const double defaultLongitude = 116.4074;
  
  // 分类常量 - 扩展为更通用的收藏类型
  static const List<String> defaultCategories = [
    '餐厅美食',
    '咖啡茶饮',
    '书籍阅读',
    '景点旅游',
    '购物商店',
    '工作学习',
    '娱乐休闲',
    '运动健身',
    '艺术文化',
    '科技数码',
    '生活服务',
    '其他收藏',
  ];
  
  // 分类颜色 - 更新为新的分类
  static const Map<String, String> categoryColors = {
    '餐厅美食': '#FF6B6B',
    '咖啡茶饮': '#4ECDC4',
    '书籍阅读': '#45B7D1',
    '景点旅游': '#96CEB4',
    '购物商店': '#FFEAA7',
    '工作学习': '#DDA0DD',
    '娱乐休闲': '#98D8C8',
    '运动健身': '#FF8A80',
    '艺术文化': '#CE93D8',
    '科技数码': '#81C784',
    '生活服务': '#FFB74D',
    '其他收藏': '#F7DC6F',
  };
  
  // 文件路径
  static const String imagesPath = 'images';
  static const String markersPath = 'markers';
  
  // 分享相关
  static const String shareBaseUrl = 'https://favsany.com/share/';
  
  // 设置键名
  static const String keyFirstLaunch = 'first_launch';
  static const String keyThemeMode = 'theme_mode';
  static const String keyDefaultPrivacy = 'default_privacy';
  static const String keyMapType = 'map_type';
  static const String keyAutoBackup = 'auto_backup';
  static const String keyLocationPermission = 'location_permission';
  
  // 限制
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int maxDescriptionLength = 500;
  static const int maxTitleLength = 100;
  static const int maxTagsCount = 10;
}
