import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';

import '../constants/app_constants.dart';
import '../constants/app_colors.dart';
import '../providers/map_provider.dart';

import '../services/map_service.dart';
import '../services/share_service.dart';
import 'add_edit_marker_screen.dart';
import 'favorites_list_screen.dart';
import 'settings_screen.dart';

class MapHomeScreen extends StatefulWidget {
  const MapHomeScreen({super.key});

  @override
  State<MapHomeScreen> createState() => _MapHomeScreenState();
}

class _MapHomeScreenState extends State<MapHomeScreen> with TickerProviderStateMixin {
  final MapService _mapService = MapService();
  GoogleMapController? _mapController;
  bool _isPermissionGranted = false;
  bool _isSearchExpanded = false;
  late AnimationController _searchAnimationController;
  late Animation<double> _searchAnimation;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeMap();
    _requestLocationPermission();
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _searchAnimation = CurvedAnimation(
      parent: _searchAnimationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _searchAnimationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _initializeMap() async {
    await _mapService.initialize();
    if (mounted) {
      context.read<MapProvider>().loadMarkers();
    }
  }

  Future<void> _requestLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }
      
      setState(() {
        _isPermissionGranted = permission == LocationPermission.whileInUse ||
                              permission == LocationPermission.always;
      });
    } catch (e) {
      setState(() {
        _isPermissionGranted = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('我的喜好地图'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(Icons.menu, color: AppColors.primary),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _isSearchExpanded ? Icons.close : Icons.search,
              color: AppColors.primary,
            ),
            onPressed: _toggleSearch,
          ),
          const SizedBox(width: 8),
        ],

      ),
      body: Consumer<MapProvider>(
        builder: (context, mapProvider, child) {
          return Stack(
            children: [
              // 地图视图
              GoogleMap(
                onMapCreated: (GoogleMapController controller) {
                  _mapController = controller;
                  _getCurrentLocation();
                },
                initialCameraPosition: const CameraPosition(
                  target: LatLng(
                    AppConstants.defaultLatitude,
                    AppConstants.defaultLongitude,
                  ),
                  zoom: AppConstants.defaultZoomLevel,
                ),
                myLocationEnabled: _isPermissionGranted,
                myLocationButtonEnabled: false,
                markers: _buildMarkers(mapProvider.filteredMarkers),
                onTap: (LatLng position) {
                  mapProvider.clearSelectedMarker();
                },
              ),
              
              // 折叠搜索框
              _buildCollapsibleSearchBar(),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addMarkerAtCurrentLocation,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 2,
        child: const Icon(Icons.add),
      ),
      drawer: _buildDrawer(),
    );
  }

  void _toggleSearch() {
    setState(() {
      _isSearchExpanded = !_isSearchExpanded;
    });

    if (_isSearchExpanded) {
      _searchAnimationController.forward();
    } else {
      _searchAnimationController.reverse();
      _searchController.clear();
    }
  }

  Widget _buildCollapsibleSearchBar() {
    return AnimatedBuilder(
      animation: _searchAnimation,
      builder: (context, child) {
        return Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: GestureDetector(
            onTap: () {
              // 点击搜索区域外部时关闭搜索框
              if (_isSearchExpanded) {
                _toggleSearch();
              }
            },
            child: Container(
              height: _searchAnimation.value * 80,
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: _searchAnimation.value > 0 ? [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ] : null,
              ),
              child: _searchAnimation.value > 0.5 ? _buildSearchContent() : null,
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GestureDetector(
        onTap: () {
          // 阻止点击搜索框时关闭
        },
        child: Container(
          height: 48,
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
          ),
          child: TextField(
            controller: _searchController,
            autofocus: true,
            decoration: InputDecoration(
              hintText: '搜索地点、标签...',
              border: InputBorder.none,
              focusedBorder: InputBorder.none,
              enabledBorder: InputBorder.none,
              errorBorder: InputBorder.none,
              disabledBorder: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              hintStyle: const TextStyle(color: Colors.grey, fontSize: 16),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        setState(() {});
                      },
                      icon: const Icon(Icons.clear, size: 18),
                      color: Colors.grey[600],
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                    )
                  : null,
            ),
            style: const TextStyle(fontSize: 16),
            onChanged: (value) {
              setState(() {});
              // TODO: 实现搜索功能
            },
            onSubmitted: (value) {
              // TODO: 处理搜索提交
              _toggleSearch();
            },
          ),
        ),
      ),
    );
  }



  Set<Marker> _buildMarkers(List<dynamic> markers) {
    return markers.map((marker) {
      return Marker(
        markerId: MarkerId(marker.id ?? 'marker_${marker.hashCode}'),
        position: LatLng(marker.latitude, marker.longitude),
        infoWindow: InfoWindow(
          title: marker.title,
          snippet: marker.description,
        ),
        onTap: () {
          context.read<MapProvider>().setSelectedMarker(marker);
        },
      );
    }).toSet();
  }

  Future<void> _getCurrentLocation() async {
    if (!_isPermissionGranted || _mapController == null) return;

    try {
      Position position = await Geolocator.getCurrentPosition();
      final latLng = LatLng(position.latitude, position.longitude);
      
      await _mapController!.animateCamera(
        CameraUpdate.newLatLng(latLng),
      );
      
      if (mounted) {
        context.read<MapProvider>().setCurrentLocation(latLng);
      }
    } catch (e) {
      // 获取位置失败，使用默认位置
    }
  }

  Future<void> _addMarkerAtCurrentLocation() async {
    LatLng? location;
    
    if (_isPermissionGranted) {
      try {
        Position position = await Geolocator.getCurrentPosition();
        location = LatLng(position.latitude, position.longitude);
      } catch (e) {
        location = const LatLng(
          AppConstants.defaultLatitude,
          AppConstants.defaultLongitude,
        );
      }
    } else {
      location = const LatLng(
        AppConstants.defaultLatitude,
        AppConstants.defaultLongitude,
      );
    }

    if (mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => AddEditMarkerScreen(
            initialLocation: location,
          ),
        ),
      );
    }
  }

  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: const BoxDecoration(color: Colors.white),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(Icons.map, size: 48, color: Colors.black),
                const SizedBox(height: 16),
                const Text(
                  AppConstants.appNameChinese,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '记录你的喜好地点',
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.favorite),
            title: const Text('收藏列表'),
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const FavoritesListScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('设置'),
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SettingsScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('分享'),
            onTap: () {
              Navigator.pop(context);
              _shareApp();
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.info),
            title: const Text('关于'),
            onTap: () {
              Navigator.pop(context);
              _showAboutDialog();
            },
          ),
        ],
      ),
    );
  }

  Future<void> _shareApp() async {
    try {
      await ShareService().shareApp();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('分享失败: $e')),
        );
      }
    }
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appNameChinese,
      applicationVersion: AppConstants.appVersion,
      applicationIcon: const Icon(Icons.map, size: 48),
      children: [
        const Text('一个基于地理位置的个人兴趣收藏工具'),
      ],
    );
  }
}
